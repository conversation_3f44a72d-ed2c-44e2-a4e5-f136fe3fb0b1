import anthropic
import re
import argparse
import sys

class PromptGenerator:
    def __init__(self):
        self.ANTHROPIC_API_KEY = ""  # 需要用户提供API key
        self.ANTHROPIC_BASE_URL = ""  # 需要用户提供base URL
        self.MODEL_NAME = "claude-3-5-sonnet-20240620"
        self.client = None

    def initialize_client(self, api_key, base_url=None):
        """初始化Anthropic客户端"""
        self.ANTHROPIC_API_KEY = api_key
        self.ANTHROPIC_BASE_URL = base_url
        self.client = anthropic.Anthropic(api_key=self.ANTHROPIC_API_KEY,base_url=self.ANTHROPIC_BASE_URL)

    def extract_between_tags(self, tag: str, string: str, strip: bool = False) -> list[str]:
        """提取XML标签之间的内容"""
        ext_list = re.findall(f"<{tag}>(.+?)</{tag}>", string, re.DOTALL)
        if strip:
            ext_list = [e.strip() for e in ext_list]
        return ext_list

    def remove_empty_tags(self, text):
        """移除空标签"""
        return re.sub(r'\n<(\w+)>\s*</\1>\n', '', text, flags=re.DOTALL)

    def strip_last_sentence(self, text):
        """移除最后一个句子(如果以'Let me know'开头)"""
        sentences = text.split('. ')
        if sentences[-1].startswith("Let me know"):
            sentences = sentences[:-1]
            result = '. '.join(sentences)
            if result and not result.endswith('.'):
                result += '.'
            return result
        return text

    def extract_prompt(self, metaprompt_response):
        """从metaprompt响应中提取提示词"""
        between_tags = self.extract_between_tags("Instructions", metaprompt_response)[0]
        return between_tags[:1000] + self.strip_last_sentence(
            self.remove_empty_tags(self.remove_empty_tags(between_tags[1000:]).strip()).strip()
        )

    def extract_variables(self, prompt):
        """提取提示词中的变量"""
        pattern = r'{([^}]+)}'
        variables = re.findall(pattern, prompt)
        return set(variables)

    def find_free_floating_variables(self, prompt):
        """查找浮动变量"""
        variable_usages = re.findall(r'\{\$[A-Z0-9_]+\}', prompt)
        free_floating_variables = []
        
        for variable in variable_usages:
            preceding_text = prompt[:prompt.index(variable)]
            open_tags = set()
            
            i = 0
            while i < len(preceding_text):
                if preceding_text[i] == '<':
                    if i + 1 < len(preceding_text) and preceding_text[i + 1] == '/':
                        closing_tag = preceding_text[i + 2:].split('>', 1)[0]
                        open_tags.discard(closing_tag)
                        i += len(closing_tag) + 3
                    else:
                        opening_tag = preceding_text[i + 1:].split('>', 1)[0]
                        open_tags.add(opening_tag)
                        i += len(opening_tag) + 2
                else:
                    i += 1
                    
            if not open_tags:
                free_floating_variables.append(variable)
                
        return free_floating_variables

    def remove_inapt_floating_variables(self, prompt):
        """移除不合适的浮动变量"""
        with open('prompt_floating_variables.txt', 'r') as f:
            remove_floating_variables_prompt = f.read()
            
        message = self.client.messages.create(
            model=self.MODEL_NAME,
            messages=[{'role': "user", "content": remove_floating_variables_prompt.replace("{$PROMPT}", prompt)}],
            max_tokens=4096,
            temperature=0
        ).content[0].text
        
        return self.extract_between_tags("rewritten_prompt", message)[0]

    def generate_prompt(self, task, variables=None):
        """生成提示词模板"""
        # 读取metaprompt文本
        with open('metaprompt.txt', 'r') as f:
            metaprompt = f.read()

        # 准备变量字符串
        variable_string = ""
        if variables:
            for variable in variables:
                variable_string += f"\n{{{variable.upper()}}}"

        # 替换任务并创建提示词
        prompt = metaprompt.replace("{{TASK}}", task)
        assistant_partial = "<Inputs>"
        if variable_string:
            assistant_partial += f"{variable_string}\n</Inputs>\n<Instructions Structure>"

        # 获取Claude的响应
        message = self.client.messages.create(
            model=self.MODEL_NAME,
            max_tokens=4096,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                },
                {
                    "role": "assistant",
                    "content": assistant_partial
                }
            ],
            temperature=0
        ).content[0].text

        # 提取和处理提示词模板
        extracted_prompt_template = self.extract_prompt(message)
        variables = self.extract_variables(message)

        # 检查和处理浮动变量
        floating_variables = self.find_free_floating_variables(extracted_prompt_template)
        if floating_variables:
            extracted_prompt_template = self.remove_inapt_floating_variables(extracted_prompt_template)

        return extracted_prompt_template, variables

    def test_prompt(self, prompt_template, variables):
        """测试生成的提示词模板"""
        variable_values = {}
        for variable in variables:
            value = input(f"Enter value for variable {variable}: ")
            variable_values[variable] = value

        prompt_with_variables = prompt_template
        for variable in variable_values:
            prompt_with_variables = prompt_with_variables.replace("{" + variable + "}", variable_values[variable])

        message = self.client.messages.create(
            model=self.MODEL_NAME,
            max_tokens=4096,
            messages=[
                {
                    "role": "user",
                    "content": prompt_with_variables
                }
            ]
        ).content[0].text

        return message

def main():
    parser = argparse.ArgumentParser(description='Prompt Generator Tool')
    parser.add_argument('--api-key', required=True, help='Anthropic API Key')
    parser.add_argument('--base-url', required=True, help='Anthropic base URL')
    parser.add_argument('--task', required=True, help='Task description')
    parser.add_argument('--variables', nargs='*', help='Optional variables (space separated)')
    parser.add_argument('--test', action='store_true', help='Test the generated prompt')
    
    args = parser.parse_args()

    # 创建生成器实例
    generator = PromptGenerator()
    
    try:
        # 初始化客户端
        generator.initialize_client(args.api_key,args.base_url)
        
        # 生成提示词
        prompt_template, variables = generator.generate_prompt(args.task, args.variables)
        
        print("\nGenerated Variables:")
        print(variables)
        print("\nGenerated Prompt Template:")
        print(prompt_template)
        
        # 如果需要测试
        if args.test:
            print("\nTesting the prompt:")
            result = generator.test_prompt(prompt_template, variables)
            print("\nTest Result:")
            print(result)
            
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
