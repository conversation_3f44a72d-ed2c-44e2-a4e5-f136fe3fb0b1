# Prompt Generator Tool

这是一个将Jupyter Notebook版本的Prompt Generator转换成的Python命令行工具。它可以帮助你生成AI提示词模板。

## 安装依赖

```bash
pip install anthropic
```

## 使用方法

基本用法:

```bash
python claude_prompt_generator.py --api-key YOUR_API_KEY --task "你的任务描述"
```

带可选变量:

```bash
python claude_prompt_generator.py --api-key YOUR_API_KEY --task "你的任务描述" --variables VARIABLE1 VARIABLE2
```

测试生成的提示词:

```bash
python claude_prompt_generator.py --api-key YOUR_API_KEY --task "你的任务描述" --test
```

### 参数说明

- `--api-key`: (必需) Anthropic API密钥
- `--task`: (必需) 任务描述
- `--variables`: (可选) 空格分隔的变量列表
- `--test`: (可选) 测试生成的提示词

### 示例

```bash
# 简单示例
python claude_prompt_generator.py --api-key "your-api-key" --task "Draft an email responding to a customer complaint"

# 带变量的示例
python claude_prompt_generator.py --api-key "your-api-key" --task "Choose an item from a menu" --variables MENU PREFERENCES

# 测试生成的提示词
python claude_prompt_generator.py --api-key "your-api-key" --task "Rate a resume according to a rubric" --variables RESUME RUBRIC --test
```

## 文件说明

- `prompt_generator.py`: 主程序文件
- `metaprompt.txt`: 包含metaprompt模板
- `prompt_floating_variables.txt`: 处理浮动变量的提示词
- `README.md`: 使用说明文档

## 注意事项

1. 确保你有有效的Anthropic API密钥
2. 任务描述应该清晰具体
3. 变量名应该是描述性的，全大写
4. 测试模式会要求你为每个变量提供值

## 错误处理

如果遇到错误，程序会显示错误信息并退出。常见错误包括：
- API密钥无效
- 网络连接问题
- 输入格式不正确

## 开发说明

这个工具是从Jupyter Notebook版本转换而来，保持了原有的核心功能，同时添加了命令行接口。主要改进包括：
- 更好的错误处理
- 命令行参数支持
- 模块化的代码结构
- 更容易集成到其他工具中
