I will give you a prompt template with one or more usages of variables (capitalized words between curly braces with a dollar sign). Some of these usages are erroneous and should be replaced with the unadorned variable name (possibly with minor cosmetic changes to the sentence). What does it mean for a usage to be "erroneous"? It means that when the variable is replaced by its actual value, the sentence would be ungrammatical, nonsensical, or otherwise inappropriate.

For example, take this prompt:

<example_prompt>
You are an AI assistant that specializes in helping users grade a resume according to a rubric that I will provide. Your task is to read the {$RESUME} closely and evaluate it according to each of the criteria listed in the {$RUBRIC}.

Here is the resume you will be assessing:
<resume>
{$RESUME}
</resume>

And here is the rubric you will be using:
<rubric>
{$RUBRIC}
</rubric>

First, in a <scratchpad>, go through each of the criteria in the rubric and consider how well the resume meets each one. Then, provide a <score> for that individual criteria. Consider individual elements of the resume and whether or not they meet the criteria.

Once you have scored each criteria, provide an overall <score> for the resume and justify your assessment in <justification> tags.
</example_prompt>

Here are the variables, their texts and usages, and whether or not the usages are erroneous. A *variable* is a word or phrase that is used as a placeholder for various inputs that will be provided by the user. In the prompt, variables are denoted by surrounding brackets and a dollar sign, like this:

{$VARIABLE}

The *text* of a usage is the sentence or phrase in which the variable appears. The *apt* tag indicates whether the variable has been aptly and appropriately used. If the usage is actually intended to just be the plain text of the variable name, it's inapt.

<variables>
<variable>
<name>
{$RESUME}
</name>
<usages>
<usage>
<text>
Your task is to read the {$RESUME} closely and evaluate it according to each of the criteria listed in the {$RUBRIC}.
<text>
<thinking>
Replacing "{$RESUME}" with an actual resume would not make sense in the context of this sentence.
Replacing "{$MENU}" with the word "resume" would make more sense.
</thinking>
<apt>
No
</apt>
<usage>
<usage>
<text>
Here is the resume you will be assessing:
<resume>
{$RESUME}
</resume>
<text>
<thinking>
Here, the "{$RESUME}" variable is introduced by the phrase "Here is the resume you will be assessing:" and wrapped in XML tags. Substituting the full resume would make total sense. In contrast, replacing it with the mere *word* "resume" would not be correct because there's an expectation that the actual resume should go here.
</thinking>
<apt>
Yes
</apt>
<usage>
</usages>
</variable>
</variables>

In general, inline variable usages (not surrounded by XML tags) are only apt when they BOTH 1. refer to a variable that would be expected to be quite short, and also 2. exist within grammatical structures that would make sense after a subsitution.

Now that you've read and internalized the examples, please consider the following prompt:
<prompt>
{$PROMPT}
</prompt>

Create an output like the <variables> block above, in which you list all the variables used in the prompt, their usages, your thinking about their aptness, and finally whether they are apt or inapt. While thinking, first consider each replacement before reaching a conclusion about aptness. If the usage seems grievously inapt (err on the side of presuming correctness), propose a rewrite.

Then, rewrite the prompt. Adapt each inapt variable use according to the remedy you proposed in the corresponding <thinking> tags. Put this rewrite in a <rewritten_prompt> tag. For apt variable usages, don't make any changes to that area of the prompt. If all usages are deemed apt, you may indicate this by simply writing "No changes." within the <rewritten_prompt> tags.

Important rule: Your rewritten prompt must always include each variable at least once. If there is a variable for which all usages are inapt, introduce the variable at the beginning in an XML-tagged block, analogous to some of the usages in the examples above.
