<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width initial-scale=1">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
  <style>
@media (max-width: 767px) { body { padding: 15px; } }
body { box-sizing: border-box;
       min-width: 200px;
       max-width: 980px;
       margin: 0 auto;
       padding: 45px; }
  </style>
  <title>Marked.js</title>
</head>
<body>
  <div id="$m" class="markdown-body"></div>
  <textarea id="$t" style="display:none">
## Hello World
*Write* `your` **Markdown** here.
> This is a blockquote.
```javascript
console.log("Hello, world!");
```
- [ ] Task list item 1
- [ ] Task list item 2

1. Numbered list item 1
2. Numbered list item 2

<font color=red>我是红色</font>
## 🤯 一句话写代码！用 AI 助你开挂，效率翻倍！ 🚀

🔥 AI 浪潮来袭，你还在为繁琐的代码而烦恼吗？ 🤔 别担心，Cursor 来帮你！ 💪 它不仅能用自然语言写代码，还能帮你写作、编辑、自动生成工作流，简直是程序员和创作者的福音！ 🤩

🌟 🌟 🌟

**Cursor 的五大开挂用法：**

1️⃣ ✍️ **写代码**:
    * 🤯 用人话写代码！只需描述你的需求，Cursor 就能自动生成代码，再也不用苦苦敲代码啦！
    * 💪 不满意？直接跟它说，修改起来超方便！
    * ✨ 高亮、语法检查、代码补全，一应俱全，让你写代码更轻松！

2️⃣ ✍️ **写作**:
    * ✍️ 写文章大纲？Cursor 帮你搞定！
    * 🖊️ 想修改文稿风格？一句话的事儿！
    * 💡 词穷？Cursor 会帮你补全句子，让你文思泉涌！

3️⃣ ✂️ **编辑**:
    * ✂️ 修改段落？直接用自然语言描述你的需求，Cursor 帮你一键完成！
    * 📑 支持 Markdown、HTML、代码等多种格式，满足你的各种编辑需求！

4️⃣ 🤖 **自动生成工作流**:
    * 🤖 连接 Define 等平台，自动生成工作流，解放你的双手！
    * 📑 参考现有的配置文件，让 AI 快速生成新的工作流！
    * 💡 遇到问题？Cursor 会帮你排查错误，并给出解决方案！

5️⃣ 💻 **搞定开源项目**:
    * 💻 打开开源项目，让 Cursor 帮你解释代码和文档！
    * 💪 想修改项目功能？告诉 Cursor 你的想法，它会帮你添加新功能！
    * 🌎 支持多种语言翻译，让你轻松理解项目代码！

**Cursor 还能做更多事情，快来体验吧！**
  </textarea>
  <script>$m.innerHTML = marked.parse($t.value);</script>
</body>
</html>
