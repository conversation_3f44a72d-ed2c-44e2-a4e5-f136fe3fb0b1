<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Markdown 笔记卡片生成器</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- DOMPurify for sanitizing HTML -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dompurify/2.4.0/purify.min.js"></script>
    <!-- HTML2Canvas 导出功能 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- 字体加载 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Courier+Prime&family=Noto+Serif+SC:wght@400;700&family=Roboto+Mono&family=Source+Code+Pro&family=Lora&family=Playfair+Display&family=Ma+Shan+Zheng&family=Noto+Sans+SC&family=ZCOOL+XiaoWei&family=ZCOOL+QingKe+HuangYou&display=swap"
      rel="stylesheet"
    />
    <style>
      .theme-light {
        --bg-primary: #f8fafc;
        --bg-card: rgba(255, 255, 255, 0.85);
        --text-primary: #334155;
        --accent-color: #3b82f6;
        --accent-color-light: #93c5fd;
        --shadow-color: rgba(0, 0, 0, 0.1);
        --input-bg: rgba(255, 255, 255, 0.8);
        --input-text: #334155;
        --input-border: #e2e8f0;
        --btn-primary: #3b82f6;
        --btn-primary-hover: #2563eb;
        --btn-secondary: #e2e8f0;
        --btn-secondary-hover: #cbd5e1;
        --code-bg: #f1f5f9;
        --code-text: #0f172a;
        --blockquote-border: #cbd5e1;
      }

      .theme-dark {
        --bg-primary: #0f172a;
        --bg-card: rgba(30, 41, 59, 0.85);
        --text-primary: #e2e8f0;
        --accent-color: #60a5fa;
        --accent-color-light: #3b82f6;
        --shadow-color: rgba(0, 0, 0, 0.5);
        --input-bg: rgba(15, 23, 42, 0.8);
        --input-text: #e2e8f0;
        --input-border: #334155;
        --btn-primary: #3b82f6;
        --btn-primary-hover: #2563eb;
        --btn-secondary: #334155;
        --btn-secondary-hover: #475569;
        --code-bg: #1e293b;
        --code-text: #e2e8f0;
        --blockquote-border: #475569;
      }

      .theme-warm {
        --bg-primary: #fffbeb;
        --bg-card: rgba(254, 243, 199, 0.85);
        --text-primary: #78350f;
        --accent-color: #f59e0b;
        --accent-color-light: #fbbf24;
        --shadow-color: rgba(120, 53, 15, 0.1);
        --input-bg: rgba(255, 251, 235, 0.8);
        --input-text: #78350f;
        --input-border: #fbbf24;
        --btn-primary: #f59e0b;
        --btn-primary-hover: #d97706;
        --btn-secondary: #fef3c7;
        --btn-secondary-hover: #fde68a;
        --code-bg: #fef3c7;
        --code-text: #78350f;
        --blockquote-border: #fbbf24;
      }

      .theme-ocean {
        --bg-primary: #ecfeff;
        --bg-card: rgba(207, 250, 254, 0.85);
        --text-primary: #155e75;
        --accent-color: #06b6d4;
        --accent-color-light: #67e8f9;
        --shadow-color: rgba(21, 94, 117, 0.1);
        --input-bg: rgba(236, 254, 255, 0.8);
        --input-text: #155e75;
        --input-border: #06b6d4;
        --btn-primary: #06b6d4;
        --btn-primary-hover: #0891b2;
        --btn-secondary: #cffafe;
        --btn-secondary-hover: #a5f3fc;
        --code-bg: #cffafe;
        --code-text: #155e75;
        --blockquote-border: #06b6d4;
      }

      .theme-forest {
        --bg-primary: #ecfdf5;
        --bg-card: rgba(209, 250, 229, 0.85);
        --text-primary: #065f46;
        --accent-color: #10b981;
        --accent-color-light: #6ee7b7;
        --shadow-color: rgba(6, 95, 70, 0.1);
        --input-bg: rgba(236, 253, 245, 0.8);
        --input-text: #065f46;
        --input-border: #10b981;
        --btn-primary: #10b981;
        --btn-primary-hover: #059669;
        --btn-secondary: #d1fae5;
        --btn-secondary-hover: #a7f3d0;
        --code-bg: #d1fae5;
        --code-text: #065f46;
        --blockquote-border: #10b981;
      }

      body {
        background-color: var(--bg-primary);
        color: var(--text-primary);
        transition: all 0.3s ease;
      }

      .card {
        background-color: var(--bg-card);
        box-shadow: 0 8px 30px var(--shadow-color);
        transition: all 0.3s ease;
      }

      .floating-bg {
        position: fixed;
        width: 40vw;
        height: 40vw;
        border-radius: 50%;
        filter: blur(80px);
        opacity: 0.6;
        z-index: -1;
        animation: floating 15s infinite ease-in-out;
      }

      .bg-1 {
        background: var(--accent-color);
        top: -10%;
        left: -10%;
        animation-delay: 0s;
      }

      .bg-2 {
        background: var(--accent-color-light);
        bottom: -10%;
        right: -10%;
        animation-delay: -5s;
      }

      @keyframes floating {
        0% {
          transform: translate(0, 0) rotate(0deg);
        }
        25% {
          transform: translate(5%, 5%) rotate(5deg);
        }
        50% {
          transform: translate(0, 10%) rotate(0deg);
        }
        75% {
          transform: translate(-5%, 5%) rotate(-5deg);
        }
        100% {
          transform: translate(0, 0) rotate(0deg);
        }
      }

      .en-text {
        /* 英文默认字体 */
        font-family: "Courier Prime", monospace;
      }

      .zh-text {
        /* 中文默认字体 */
        font-family: "Noto Serif SC", serif;
      }

      /* 自定义滚动条 */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: transparent;
      }

      ::-webkit-scrollbar-thumb {
        background: var(--accent-color);
        border-radius: 4px;
      }

      /* 固定卡片尺寸 */
      .card-container {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        width: 100%;
      }

      /* 调整卡片内容自适应 */
      .content-wrapper {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding: 1.5rem;
      }

      /* 主按钮样式 */
      .btn-primary {
        background-color: var(--btn-primary);
        color: white;
        transition: all 0.3s ease;
      }

      .btn-primary:hover {
        background-color: var(--btn-primary-hover);
      }

      /* 次要按钮样式 */
      .btn-secondary {
        background-color: var(--btn-secondary);
        color: var(--text-primary);
        transition: all 0.3s ease;
      }

      .btn-secondary:hover {
        background-color: var(--btn-secondary-hover);
      }

      /* 水印效果 */
      .watermark {
        position: absolute;
        right: 10px;
        bottom: 5px;
        font-size: 0.7rem;
        opacity: 0.5;
      }

      /* Markdown 样式覆盖 */
      #content-preview h1,
      #content-preview h2,
      #content-preview h3,
      #content-preview h4,
      #content-preview h5,
      #content-preview h6 {
        font-weight: bold;
        margin-top: 1em;
        margin-bottom: 0.5em;
      }

      #content-preview h1 {
        font-size: 1.8em;
      }

      #content-preview h2 {
        font-size: 1.5em;
      }

      #content-preview h3 {
        font-size: 1.3em;
      }

      #content-preview p {
        margin-bottom: 1em;
      }

      #content-preview ul,
      #content-preview ol {
        margin-bottom: 1em;
        padding-left: 1.5em;
      }

      #content-preview ul {
        list-style-type: disc;
      }

      #content-preview ol {
        list-style-type: decimal;
      }

      #content-preview li {
        margin-bottom: 0.3em;
      }

      #content-preview blockquote {
        border-left: 4px solid var(--blockquote-border);
        padding-left: 1em;
        margin-left: 0;
        margin-right: 0;
        margin-bottom: 1em;
        font-style: italic;
      }

      #content-preview code {
        font-family: "Source Code Pro", monospace;
        background-color: var(--code-bg);
        color: var(--code-text);
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-size: 0.9em;
      }

      #content-preview pre {
        background-color: var(--code-bg);
        padding: 1em;
        border-radius: 5px;
        overflow-x: auto;
        margin-bottom: 1em;
      }

      #content-preview pre code {
        background-color: transparent;
        padding: 0;
        border-radius: 0;
      }

      #content-preview a {
        color: var(--accent-color);
        text-decoration: underline;
      }

      #content-preview hr {
        border: 0;
        border-top: 1px solid var(--input-border);
        margin: 1.5em 0;
      }

      #content-preview img {
        max-width: 100%;
        height: auto;
        border-radius: 5px;
        margin: 1em 0;
      }

      /* 调整主要布局结构 */
      @media (min-width: 768px) {
        .main-container {
          display: flex;
          justify-content: center;
          align-items: flex-start;
          gap: 2rem;
        }

        .control-panel {
          width: 40%;
          max-width: 400px;
        }

        .preview-panel {
          width: 60%;
          display: flex;
          flex-direction: column;
          align-items: center;
        }
      }

      /* 小屏幕布局调整 */
      @media (max-width: 767px) {
        .main-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
        }

        .control-panel,
        .preview-panel {
          width: 100%;
          max-width: 500px;
        }

        .preview-panel {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
      }

      /* 输入框和选择框样式 */
      input,
      textarea,
      select {
        background-color: var(--input-bg);
        color: var(--input-text);
        border-color: var(--input-border);
        transition: all 0.3s ease;
      }

      input::placeholder,
      textarea::placeholder {
        color: var(--input-text);
        opacity: 0.6;
      }

      /* 卡片预览区阴影增强 */
      .preview-shadow {
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
      }

      /* 工具提示 */
      .tooltip {
        position: relative;
        display: inline-block;
      }

      .tooltip .tooltip-text {
        visibility: hidden;
        width: 120px;
        background-color: var(--text-primary);
        color: var(--bg-primary);
        text-align: center;
        border-radius: 6px;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        margin-left: -60px;
        opacity: 0;
        transition: opacity 0.3s;
      }

      .tooltip:hover .tooltip-text {
        visibility: visible;
        opacity: 0.9;
      }

      /* 页面过渡动画 */
      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      /* Markdown编辑器工具栏 */
      .markdown-toolbar {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
      }

      .markdown-toolbar button {
        background-color: var(--btn-secondary);
        color: var(--text-primary);
        border: none;
        border-radius: 4px;
        padding: 0.3rem 0.5rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .markdown-toolbar button:hover {
        background-color: var(--btn-secondary-hover);
      }

      /* Markdown帮助模态框 */
      .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .modal.show {
        opacity: 1;
        visibility: visible;
      }

      .modal-content {
        background-color: var(--bg-primary);
        border-radius: 8px;
        padding: 1.5rem;
        max-width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
      }

      /* 示例样式 */
      .example-item {
        border-bottom: 1px solid var(--input-border);
        padding-bottom: 0.8rem;
        margin-bottom: 0.8rem;
      }

      .example-item:last-child {
        border-bottom: none;
      }

      .code-display {
        font-family: "Source Code Pro", monospace;
        background-color: var(--code-bg);
        color: var(--code-text);
        padding: 0.3rem 0.6rem;
        border-radius: 4px;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
      }

      .result-display {
        margin-top: 0.5rem;
        padding: 0.5rem;
        border-radius: 4px;
        background-color: var(--bg-card);
      }
    </style>
  </head>
  <body
    class="theme-light min-h-screen flex flex-col items-center justify-center py-8 px-4 fade-in"
  >
    <!-- 浮动背景 -->
    <div class="floating-bg bg-1"></div>
    <div class="floating-bg bg-2"></div>

    <header class="w-full max-w-4xl text-center mb-8">
      <h1 class="text-4xl font-bold mb-2">✨ Markdown 笔记卡片生成器</h1>
      <p class="text-sm opacity-75">
        将 Markdown 文本转化为精美的视觉卡片 | 支持中英文智能排版
      </p>
    </header>

    <main class="w-full max-w-5xl main-container">
      <!-- 控制面板 -->
      <div class="control-panel space-y-6">
        <div class="card p-6 rounded-xl">
          <h2 class="text-xl font-bold mb-4 flex items-center">
            <i class="fas fa-edit mr-2"></i> Markdown 编辑
          </h2>
          <div class="space-y-4">
            <!-- Markdown工具栏 -->
            <div class="markdown-toolbar">
              <button data-md="# " title="标题1"><i class="fas fa-heading"></i> 1</button>
              <button data-md="## " title="标题2"><i class="fas fa-heading"></i> 2</button>
              <button data-md="### " title="标题3"><i class="fas fa-heading"></i> 3</button>
              <button data-md="**粗体**" title="粗体"><i class="fas fa-bold"></i></button>
              <button data-md="*斜体*" title="斜体"><i class="fas fa-italic"></i></button>
              <button data-md="[链接文本](https://example.com)" title="链接"><i class="fas fa-link"></i></button>
              <button data-md="> " title="引用"><i class="fas fa-quote-right"></i></button>
              <button data-md="- " title="无序列表"><i class="fas fa-list-ul"></i></button>
              <button data-md="1. " title="有序列表"><i class="fas fa-list-ol"></i></button>
              <button data-md="```\n代码块\n```" title="代码块"><i class="fas fa-code"></i></button>
              <button data-md="---" title="分隔线"><i class="fas fa-minus"></i></button>
              <button id="markdown-help-btn" title="Markdown帮助"><i class="fas fa-question-circle"></i></button>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">卡片内容 (支持 Markdown)</label>
              <textarea
                id="content-input"
                class="w-full h-60 p-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 transition font-mono text-sm"
                placeholder="在此输入您想要展示的 Markdown 内容..."
              ></textarea>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">署名</label>
              <input
                type="text"
                id="signature-input"
                class="w-full p-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
                placeholder="您的署名"
              />
            </div>
          </div>
        </div>

        <div class="card p-6 rounded-xl">
          <h2 class="text-xl font-bold mb-4 flex items-center">
            <i class="fas fa-palette mr-2"></i> 样式选择
          </h2>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium mb-1">主题</label>
              <select
                id="theme-select"
                class="w-full p-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
              >
                <option value="light">明亮主题</option>
                <option value="dark">暗黑主题</option>
                <option value="warm">温暖主题</option>
                <option value="ocean">海洋主题</option>
                <option value="forest">森林主题</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">中文字体</label>
              <select
                id="zh-font-select"
                class="w-full p-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
              >
                <option value="'Noto Serif SC', serif">
                  思源宋体 (Noto Serif SC)
                </option>
                <option value="'Noto Sans SC', sans-serif">
                  思源黑体 (Noto Sans SC)
                </option>
                <option value="'ZCOOL XiaoWei', serif">
                  站酷小薇 (ZCOOL XiaoWei)
                </option>
                <option value="'Ma Shan Zheng', cursive">
                  马善政楷体 (Ma Shan Zheng)
                </option>
                <option value="'ZCOOL QingKe HuangYou', cursive">
                  站酷庆科黄油体 (ZCOOL QingKe HuangYou)
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">英文字体</label>
              <select
                id="en-font-select"
                class="w-full p-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
              >
                <option value="'Courier Prime', monospace">
                  Courier Prime 等宽
                </option>
                <option value="'Roboto Mono', monospace">
                  Roboto Mono 等宽
                </option>
                <option value="'Source Code Pro', monospace">
                  Source Code Pro 等宽
                </option>
                <option value="'Lora', serif">Lora 衬线</option>
                <option value="'Playfair Display', serif">
                  Playfair Display 衬线
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">卡片风格</label>
              <div class="flex space-x-2 mt-2">
                <button id="style-standard" class="btn-style-option p-2 rounded-lg border border-gray-300 text-center flex-1 active" data-style="standard">
                  <i class="fas fa-square-full block mx-auto mb-1"></i>
                  标准
                </button>
                <button id="style-border" class="btn-style-option p-2 rounded-lg border border-gray-300 text-center flex-1" data-style="border">
                  <i class="far fa-square block mx-auto mb-1"></i>
                  边框
                </button>
                <button id="style-quote" class="btn-style-option p-2 rounded-lg border border-gray-300 text-center flex-1" data-style="quote">
                  <i class="fas fa-quote-right block mx-auto mb-1"></i>
                  引用
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="preview-panel flex flex-col">
        <div class="card p-6 rounded-xl w-full mb-6 preview-shadow relative">
          <h2 class="text-xl font-bold mb-4 flex items-center">
            <i class="fas fa-eye mr-2"></i> 实时预览
          </h2>
          <div id="preview-container" class="card-container">
            <div id="card-preview" class="content-wrapper rounded-lg">
              <div id="content-preview" class="mb-4"></div>
              <div>
                <div
                  id="signature-preview"
                  class="text-right italic opacity-75"
                ></div>
                <div
                  id="date-display"
                  class="text-right text-sm opacity-50 mt-1"
                ></div>
              </div>
            </div>
          </div>
          <div class="watermark">Markdown 笔记卡片生成器</div>
        </div>

        <!-- 导出卡片部分 -->
        <div class="card p-6 rounded-xl w-full">
          <h2 class="text-xl font-bold mb-4 flex items-center">
            <i class="fas fa-download mr-2"></i> 导出卡片
          </h2>
          <div class="flex space-x-4">
            <button
              id="download-btn"
              class="flex-1 py-3 rounded-lg transition focus:outline-none focus:ring-2 focus:ring-blue-700 btn-primary flex justify-center items-center"
            >
              <i class="fas fa-download mr-2"></i> 下载图片
            </button>
            <button
              id="copy-btn"
              class="flex-1 py-3 rounded-lg transition focus:outline-none focus:ring-2 focus:ring-gray-400 btn-secondary flex justify-center items-center tooltip"
            >
              <i class="fas fa-clipboard mr-2"></i> 复制到剪贴板
              <span class="tooltip-text">复制图片到剪贴板</span>
            </button>
          </div>
        </div>

        <!-- 快速模板 -->
        <div class="card p-6 rounded-xl w-full mt-6">
          <h2 class="text-xl font-bold mb-4 flex items-center">
            <i class="fas fa-magic mr-2"></i> Markdown 模板
          </h2>
          <div class="grid grid-cols-2 gap-2">
            <button class="template-btn p-2 rounded-lg border btn-secondary text-sm" data-template="quote">名言引用</button>
            <button class="template-btn p-2 rounded-lg border btn-secondary text-sm" data-template="poem">诗词卡片</button>
            <button class="template-btn p-2 rounded-lg border btn-secondary text-sm" data-template="thought">思考随笔</button>
            <button class="template-btn p-2 rounded-lg border btn-secondary text-sm" data-template="md-cheatsheet">Markdown速查</button>
          </div>
        </div>
      </div>
    </main>

    <!-- Markdown帮助模态框 -->
    <div id="markdown-help-modal" class="modal">
      <div class="modal-content w-full max-w-2xl">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">Markdown 语法帮助</h3>
          <button id="close-markdown-help" class="text-lg opacity-70 hover:opacity-100 transition">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="space-y-4">
          <p>Markdown是一种轻量级标记语言，使用易读易写的纯文本格式编写文档，然后转换成有效的HTML文档。</p>

          <div class="grid md:grid-cols-2 gap-4">
            <div class="space-y-4">
              <div class="example-item">
                <h4 class="font-bold mb-2">标题</h4>
                <div class="code-display"># 一级标题</div>
                <div class="code-display">## 二级标题</div>
                <div class="code-display">### 三级标题</div>
              </div>

              <div class="example-item">
                <h4 class="font-bold mb-2">强调</h4>
                <div class="code-display">**粗体文本**</div>
                <div class="code-display">*斜体文本*</div>
                <div class="code-display">~~删除线文本~~</div>
              </div>

              <div class="example-item">
                <h4 class="font-bold mb-2">列表</h4>
                <div class="code-display">- 无序列表项</div>
                <div class="code-display">1. 有序列表项</div>
              </div>

              <div class="example-item">
                <h4 class="font-bold mb-2">链接与图片</h4>
                <div class="code-display">[链接文字](https://example.com)</div>
                <div class="code-display">![图片描述](图片URL)</div>
              </div>
            </div>

            <div class="space-y-4">
              <div class="example-item">
                <h4 class="font-bold mb-2">引用</h4>
                <div class="code-display">> 这是一段引用的文字</div>
              </div>

              <div class="example-item">
                <h4 class="font-bold mb-2">代码</h4>
                <div class="code-display">`行内代码`</div>
                <div class="code-display">```
代码块
可以包含多行代码
```</div>
              </div>

              <div class="example-item">
                <h4 class="font-bold mb-2">分隔线</h4>
                <div class="code-display">---</div>
              </div>

              <div class="example-item">
                <h4 class="font-bold mb-2">表格</h4>
                <div class="code-display">| 表头1 | 表头2 |
| ------ | ------ |
| 单元格 | 单元格 |
| 单元格 | 单元格 |</div>
              </div>
            </div>
          </div>

          <div class="mt-4 text-center">
            <button id="try-example" class="btn-primary px-4 py-2 rounded-lg">尝试示例</button>
          </div>
        </div>
      </div>
    </div>

    <footer class="mt-12 text-center text-sm opacity-50">
      <p class="mb-1">
        Markdown 笔记卡片生成器 &copy; 2025 | 使用 HTML5, Tailwind CSS 和 JavaScript 构建
      </p>
      <div class="flex justify-center space-x-4 mt-2">
        <a href="#" class="hover:text-blue-500 transition">关于</a>
        <a href="#" class="hover:text-blue-500 transition">使用指南</a>
        <a href="#" class="hover:text-blue-500 transition">隐私政策</a>
      </div>
    </footer>

    <script>
      // 工具函数
      const $ = (selector) => document.querySelector(selector);
      const $ = (selector) => document.querySelectorAll(selector);

      // 获取DOM元素
      const contentInput = $("#content-input");
      const signatureInput = $("#signature-input");
      const themeSelect = $("#theme-select");
      const zhFontSelect = $("#zh-font-select");
      const enFontSelect = $("#en-font-select");
      const downloadBtn = $("#download-btn");
      const copyBtn = $("#copy-btn");
      const contentPreview = $("#content-preview");
      const signaturePreview = $("#signature-preview");
      const dateDisplay = $("#date-display");
      const cardPreview = $("#card-preview");
      const previewContainer = $("#preview-container");
      const styleButtons = $(".btn-style-option");
      const templateButtons = $(".template-btn");
      const markdownToolbarButtons = $(".markdown-toolbar button");
      const markdownHelpBtn = $("#markdown-help-btn");
      const markdownHelpModal = $("#markdown-help-modal");
      const closeMarkdownHelp = $("#close-markdown-help");
      const tryExampleBtn = $("#try-example");

      // 模板内容
      const templates = {
        quote: {
          content: "> 人生如逆旅，我亦是行人。\n> Life is a journey, and I am just a traveler.",
          signature: "苏轼"
        },
        poem: {
          content: "## 春晓\n\n春眠不觉晓，  \n处处闻啼鸟。  \n夜来风雨声，  \n花落知多少。",
          signature: "孟浩然"
        },
        thought: {
          content: "# 思考随笔\n\n**静水流深**，沧笙踏歌；  \n*遥山长云*，曼指波弄。\n\n时光荏苒，不负此生。",
          signature: "昨日的思考"
        },
        "md-cheatsheet": {
          content: "# Markdown 速查表\n\n## 标题\n\n# 一级标题\n## 二级标题\n### 三级标题\n\n## 强调\n\n**粗体** *斜体* ~~删除线~~\n\n## 列表\n\n- 项目一\n- 项目二\n  - 子项目\n\n1. 第一步\n2. 第二步\n\n## 引用\n\n> 这是一段引用文字\n\n## 代码\n\n`行内代码`\n\n```\n// 代码块\nfunction example() {\n  return '示例代码';\n}\n```\n\n## 链接和图片\n\n[链接文字](https://example.com)\n\n![图片描述](图片URL)\n\n## 表格\n\n| 姓名 | 年龄 |\n| ---- | ---- |\n| 张三 | 25 |\n| 李四 | 30 |",
          signature: "Markdown爱好者"
        }
      };

      // 初始化Marked.js配置
      marked.setOptions({
        breaks: true, // 启用GFM换行符
        gfm: true,    // 启用GitHub风格的Markdown
        mangle: false,
        headerIds: false,
        headerPrefix: '',
        smartypants: true
      });

      // 初始化日期显示
      const updateDate = () => {
        const now = new Date();
        const options = { year: "numeric", month: "long", day: "numeric" };
        dateDisplay.textContent = now.toLocaleDateString("zh-CN", options);
      };
      updateDate();

      // 检测语言类型 (简单判断)
      const detectLanguage = (text) => {
        // 检查是否包含中文字符
        const hasChineseChar = /[\u4e00-\u9fa5]/.test(text);
        // 检查是否包含英文字符
        const hasEnglishChar = /[a-zA-Z]/.test(text);

        if (hasChineseChar && hasEnglishChar) return "mixed";
        if (hasChineseChar) return "zh";
        return "en";
      };

      // 更新预览
      const updatePreview = () => {
        const content = contentInput.value;
        const signature = signatureInput.value;
        const activeStyle = document.querySelector('.btn-style-option.active').dataset.style;

        // 处理内容，先将Markdown转换为HTML
        if (content.trim()) {
          // 使用DOMPurify清理HTML
          const htmlContent = DOMPurify.sanitize(marked.parse(content));

          // 根据不同风格应用不同样式
          let styledContent = '';

          if (activeStyle === 'standard') {
            styledContent = htmlContent;
          } else if (activeStyle === 'border') {
            styledContent = `<div class="p-4 border-2 border-current rounded-lg">${htmlContent}</div>`;
          } else if (activeStyle === 'quote') {
            styledContent = `<div class="pl-4 border-l-4 border-current">${htmlContent}</div>`;
          }

          contentPreview.innerHTML = styledContent;

          // 应用中英文字体
          applyFonts();
        } else {
          contentPreview.innerHTML = '<p class="opacity-50 italic">内容预览区域</p>';
        }

        // 处理署名
        if (signature.trim()) {
          const sigLang = detectLanguage(signature);
          const sigClassName = sigLang === "en" ? "en-text" : "zh-text";
          signaturePreview.innerHTML = `<span class="${sigClassName}">— ${signature}</span>`;
        } else {
          signaturePreview.innerHTML = "";
        }

        // 调整卡片高度以适应内容
        adjustCardHeight();
      };

      // 应用字体到预览区域
      const applyFonts = () => {
        // 查找所有文本节点并检测语言
        const textNodes = [];
        const findTextNodes = (node) => {
          if (node.nodeType === Node.TEXT_NODE) {
            if (node.textContent.trim()) {
              textNodes.push(node);
            }
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            node.childNodes.forEach(findTextNodes);
          }
        };

        // 从内容预览区域开始查找文本节点
        findTextNodes(contentPreview);

        // 为每个文本节点应用适当的字体
        textNodes.forEach(textNode => {
          if (!textNode.parentNode) return;

          const text = textNode.textContent;
          const lang = detectLanguage(text);

          // 如果父元素已经有相应的语言类，跳过
          if ((lang === "en" && textNode.parentNode.classList.contains("en-text")) ||
              (lang === "zh" && textNode.parentNode.classList.contains("zh-text"))) {
            return;
          }

          // 创建具有适当语言类的span
          const span = document.createElement('span');
          span.className = lang === "en" ? "en-text" : "zh-text";
          span.textContent = text;

          // 替换文本节点
          textNode.parentNode.replaceChild(span, textNode);
        });

        // 更新字体样式
        document.querySelectorAll(".zh-text").forEach((el) => {
          el.style.fontFamily = zhFontSelect.value;
        });

        document.querySelectorAll(".en-text").forEach((el) => {
          el.style.fontFamily = enFontSelect.value;
        });
      };

      // 自适应卡片高度
      const adjustCardHeight = () => {
        // 让卡片高度完全自适应内容
        cardPreview.style.minHeight = "";
        cardPreview.style.height = "auto";

        // 确保内容为空时也有合适的展示空间
        if (!contentInput.value.trim()) {
          contentPreview.style.minHeight = "40px";
        } else {
          contentPreview.style.minHeight = "";
        }

        // 调整预览区域的最小宽度，确保在空白时也有合适的宽度
        if (!contentInput.value.trim()) {
          cardPreview.style.minWidth = "320px";
        } else {
          cardPreview.style.minWidth = "";
        }
      };

      // 更新主题
      const updateTheme = () => {
        const theme = themeSelect.value;
        // 移除所有主题相关的类，但保留其他布局类
        document.body.classList.remove(
          "theme-light",
          "theme-dark",
          "theme-warm",
          "theme-ocean",
          "theme-forest"
        );
        // 添加新的主题类
        document.body.classList.add(`theme-${theme}`);

        // 更新输入框和选择框的样式
        updateInputStyles();
      };

      // 更新输入框和选择框样式
      const updateInputStyles = () => {
        const inputs = document.querySelectorAll("input, textarea, select");
        inputs.forEach((input) => {
          // 刷新输入框样式以应用新主题
          input.style.backgroundColor = getComputedStyle(
            document.body
          ).getPropertyValue("--input-bg");
          input.style.color = getComputedStyle(document.body).getPropertyValue(
            "--input-text"
          );
          input.style.borderColor = getComputedStyle(
            document.body
          ).getPropertyValue("--input-border");
        });
      };

      // 导出为图片
      const exportToImage = () => {
        // 创建一个临时容器来包裹卡片，以便设置正确的背景色和效果
        const tempContainer = document.createElement("div");
        tempContainer.style.position = "fixed";
        tempContainer.style.left = "-9999px";
        tempContainer.style.top = "0";
        tempContainer.style.padding = "20px";

        // 创建一个背景容器，模拟浮动背景效果
        const bgContainer = document.createElement("div");
        bgContainer.style.position = "relative";
        bgContainer.style.overflow = "hidden";
        bgContainer.style.borderRadius = "0.75rem";
        bgContainer.style.padding = "20px";

        // 添加简化的背景效果
        const accentColor = getComputedStyle(document.body).getPropertyValue(
          "--accent-color"
        );
        bgContainer.style.background = `linear-gradient(135deg, ${accentColor}22, transparent 80%)`;

        // 设置卡片容器样式
        const cardContainer = document.createElement("div");
        cardContainer.style.backgroundColor = getComputedStyle(
          document.body
        ).getPropertyValue("--bg-card");
        cardContainer.style.borderRadius = "0.75rem";
        cardContainer.style.boxShadow =
          "0 8px 30px " +
          getComputedStyle(document.body).getPropertyValue("--shadow-color");
        cardContainer.style.overflow = "hidden";

        // 克隆卡片预览元素
        const clonedCard = cardPreview.cloneNode(true);

        // 确保克隆的卡片中的所有字体都正确应用
        clonedCard.querySelectorAll(".zh-text").forEach(el => {
          el.style.fontFamily = zhFontSelect.value;
        });
        clonedCard.querySelectorAll(".en-text").forEach(el => {
          el.style.fontFamily = enFontSelect.value;
        });

        cardContainer.appendChild(clonedCard);
        bgContainer.appendChild(cardContainer);
        tempContainer.appendChild(bgContainer);
        document.body.appendChild(tempContainer);

        // 设置临时容器的尺寸
        const width = cardPreview.offsetWidth + 40; // 添加内边距
        const height = cardPreview.offsetHeight + 40;
        bgContainer.style.width = width + "px";
        bgContainer.style.height = height + "px";
        cardContainer.style.width = cardPreview.offsetWidth + "px";

        html2canvas(bgContainer, {
          backgroundColor: null, // 透明背景
          scale: 2, // 提高导出质量
          logging: false,
          allowTaint: true,
          useCORS: true,
          onclone: (clonedDoc) => {
            // 确保克隆的文档中的所有样式都正确应用
            const clonedCard = clonedDoc.querySelector("#card-preview");
            if (clonedCard) {
              clonedCard.querySelectorAll(".zh-text").forEach(el => {
                el.style.fontFamily = zhFontSelect.value;
              });
              clonedCard.querySelectorAll(".en-text").forEach(el => {
                el.style.fontFamily = enFontSelect.value;
              });
            }
          }
        }).then((canvas) => {
          // 导出图片
          const link = document.createElement("a");
          link.download = `Markdown笔记卡片_${new Date().getTime()}.png`;
          link.href = canvas.toDataURL("image/png");
          link.click();

          // 移除临时容器
          document.body.removeChild(tempContainer);
        });
      };

      // 复制到剪贴板
      const copyToClipboard = () => {
        // 复制图片到剪贴板的逻辑
        const tempContainer = document.createElement("div");
        tempContainer.style.position = "fixed";
        tempContainer.style.left = "-9999px";
        tempContainer.style.top = "0";
        tempContainer.style.padding = "20px";

        const bgContainer = document.createElement("div");
        bgContainer.style.position = "relative";
        bgContainer.style.overflow = "hidden";
        bgContainer.style.borderRadius = "0.75rem";
        bgContainer.style.padding = "20px";

        const accentColor = getComputedStyle(document.body).getPropertyValue("--accent-color");
        bgContainer.style.background = `linear-gradient(135deg, ${accentColor}22, transparent 80%)`;

        const cardContainer = document.createElement("div");
        cardContainer.style.backgroundColor = getComputedStyle(document.body).getPropertyValue("--bg-card");
        cardContainer.style.borderRadius = "0.75rem";
        cardContainer.style.boxShadow = "0 8px 30px " + getComputedStyle(document.body).getPropertyValue("--shadow-color");
        cardContainer.style.overflow = "hidden";

        const clonedCard = cardPreview.cloneNode(true);
        cardContainer.appendChild(clonedCard);
        bgContainer.appendChild(cardContainer);
        tempContainer.appendChild(bgContainer);
        document.body.appendChild(tempContainer);

        const width = cardPreview.offsetWidth + 40;
        const height = cardPreview.offsetHeight + 40;
        bgContainer.style.width = width + "px";
        bgContainer.style.height = height + "px";
        cardContainer.style.width = cardPreview.offsetWidth + "px";

        html2canvas(bgContainer, {
          backgroundColor: null,
          scale: 2,
          logging: false,
          allowTaint: true,
          useCORS: true,
          onclone: (clonedDoc) => {
            const clonedCard = clonedDoc.querySelector("#card-preview");
            if (clonedCard) {
              clonedCard.querySelectorAll(".zh-text").forEach(el => {
                el.style.fontFamily = zhFontSelect.value;
              });
              clonedCard.querySelectorAll(".en-text").forEach(el => {
                el.style.fontFamily = enFontSelect.value;
              });
            }
          }
        }).then((canvas) => {
          canvas.toBlob((blob) => {
            try {
              navigator.clipboard.write([new ClipboardItem({ "image/png": blob })])
                .then(() => {
                  alert("已复制到剪贴板！");
                })
                .catch((err) => {
                  console.error("复制失败:", err);
                  alert("复制失败，请尝试下载图片。");
                });
            } catch (e) {
              alert("您的浏览器不支持剪贴板API，请尝试下载图片。");
            }

            document.body.removeChild(tempContainer);
          });
        });
      };

      // 在textarea中插入Markdown标记
      const insertMarkdown = (mdText) => {
        const start = contentInput.selectionStart;
        const end = contentInput.selectionEnd;
        const text = contentInput.value;
        const before = text.substring(0, start);
        const after = text.substring(end);

        // 根据不同的Markdown标记和选中文本处理插入逻辑
        if (mdText === "**粗体**" || mdText === "*斜体*" || mdText === "~~删除线~~" || mdText === "[链接文本](https://example.com)") {
          const selectedText = text.substring(start, end);
          if (selectedText) {
            // 如果有选中文本，将其包裹在Markdown标记中
            let newText;
            if (mdText === "**粗体**") {
              newText = `**${selectedText}**`;
            } else if (mdText === "*斜体*") {
              newText = `*${selectedText}*`;
            } else if (mdText === "~~删除线~~") {
              newText = `~~${selectedText}~~`;
            } else if (mdText === "[链接文本](https://example.com)") {
              newText = `[${selectedText}](https://example.com)`;
            }
            contentInput.value = before + newText + after;
            contentInput.selectionStart = start;
            contentInput.selectionEnd = start + newText.length;
          } else {
            // 如果没有选中文本，插入模板
            contentInput.value = before + mdText + after;
            contentInput.selectionStart = start + mdText.length;
            contentInput.selectionEnd = start + mdText.length;
          }
        } else if (mdText === "```\n代码块\n```") {
          // 代码块特殊处理
          const selectedText = text.substring(start, end);
          const newText = selectedText ? `\`\`\`\n${selectedText}\n\`\`\`` : "```\n代码块\n```";
          contentInput.value = before + newText + after;
          contentInput.selectionStart = start + newText.length;
          contentInput.selectionEnd = start + newText.length;
        } else {
          // 对于标题、列表项等前缀类的Markdown
          // 检查光标是否在行首
          const isLineStart = start === 0 || text.charAt(start - 1) === '\n';
          if (isLineStart) {
            contentInput.value = before + mdText + after;
          } else {
            contentInput.value = before + '\n' + mdText + after;
          }
          contentInput.selectionStart = contentInput.selectionEnd = start + mdText.length + (isLineStart ? 0 : 1);
        }

        // 触发内容更新
        contentInput.focus();
        updatePreview();
      };

      // 选择模板
      const selectTemplate = (templateName) => {
        if (templates[templateName]) {
          contentInput.value = templates[templateName].content;
          signatureInput.value = templates[templateName].signature;
          updatePreview();
        }
      };

      // 切换卡片风格
      const toggleCardStyle = (button) => {
        // 移除所有按钮的active类
        styleButtons.forEach(btn => {
          btn.classList.remove('active');
          btn.style.backgroundColor = '';
          btn.style.fontWeight = '';
        });

        // 添加active类到当前按钮
        button.classList.add('active');
        button.style.backgroundColor = getComputedStyle(document.body).getPropertyValue('--accent-color-light');
        button.style.fontWeight = 'bold';

        updatePreview();
      };

      // 事件监听
      contentInput.addEventListener("input", updatePreview);
      signatureInput.addEventListener("input", updatePreview);
      themeSelect.addEventListener("change", () => {
        updateTheme();
        updatePreview();
      });
      zhFontSelect.addEventListener("change", updatePreview);
      enFontSelect.addEventListener("change", updatePreview);
      downloadBtn.addEventListener("click", exportToImage);
      copyBtn.addEventListener("click", copyToClipboard);

      // Markdown工具栏事件
      markdownToolbarButtons.forEach(button => {
        if (button.id !== "markdown-help-btn") {
          button.addEventListener("click", () => {
            insertMarkdown(button.dataset.md);
          });
        }
      });

      // Markdown帮助按钮
      markdownHelpBtn.addEventListener("click", () => {
        markdownHelpModal.classList.add("show");
      });

      // 关闭Markdown帮助
      closeMarkdownHelp.addEventListener("click", () => {
        markdownHelpModal.classList.remove("show");
      });

      // 点击模态框外部关闭
      markdownHelpModal.addEventListener("click", (e) => {
        if (e.target === markdownHelpModal) {
          markdownHelpModal.classList.remove("show");
        }
      });

      // 尝试示例按钮
      tryExampleBtn.addEventListener("click", () => {
        selectTemplate("md-cheatsheet");
        markdownHelpModal.classList.remove("show");
      });

      // 模板按钮事件
      templateButtons.forEach(button => {
        button.addEventListener("click", () => {
          selectTemplate(button.dataset.template);
        });
      });

      // 卡片风格按钮事件
      styleButtons.forEach(button => {
        button.addEventListener("click", () => {
          toggleCardStyle(button);
        });
      });

      // 为风格按钮设置初始样式
      const initialActiveButton = document.querySelector('.btn-style-option.active');
      if (initialActiveButton) {
        initialActiveButton.style.backgroundColor = getComputedStyle(document.body).getPropertyValue('--accent-color-light');
        initialActiveButton.style.fontWeight = 'bold';
      }

      // 窗口大小变化时重新调整
      window.addEventListener("resize", adjustCardHeight);

      // 初始化页面
      updateTheme();
      updatePreview();
      updateInputStyles();

      // 默认加载一个简单示例
      if (!contentInput.value) {
        contentInput.value = "## 欢迎使用 Markdown 笔记卡片生成器！\n\n这是一个示例笔记，您可以使用 **Markdown** 语法来格式化您的文本。\n\n> 尝试编辑这段文字，或者使用工具栏添加格式。";
        updatePreview();
      }
    </script>
  </body>
</html>