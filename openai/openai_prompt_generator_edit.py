from openai import OpenAI

client = OpenAI(api_key="sk-DJF634addc8da02929d365b9e27469d24ff984f2d7bP314P",base_url="https://api.gptsapi.net/v1")

META_PROMPT = """
Given a task description or existing prompt, produce a detailed system prompt to guide a language model in completing the task effectively.

# Guidelines

- Understand the Task: Grasp the main objective, goals, requirements, constraints, and expected output.
- Minimal Changes: If an existing prompt is provided, improve it only if it's simple. For complex prompts, enhance clarity and add missing elements without altering the original structure.
- Reasoning Before Conclusions**: Encourage reasoning steps before any conclusions are reached. ATTENTION! If the user provides examples where the reasoning happens afterward, REVERSE the order! NEVER START EXAMPLES WITH CONCLUSIONS!
    - Reasoning Order: Call out reasoning portions of the prompt and conclusion parts (specific fields by name). For each, determine the ORDER in which this is done, and whether it needs to be reversed.
    - Conclusion, classifications, or results should ALWAYS appear last.
- Examples: Include high-quality examples if helpful, using placeholders [in brackets] for complex elements.
   - What kinds of examples may need to be included, how many, and whether they are complex enough to benefit from placeholders.
- Clarity and Conciseness: Use clear, specific language. Avoid unnecessary instructions or bland statements.
- Formatting: Use markdown features for readability. DO NOT USE ``` CODE BLOCKS UNLESS SPECIFICALLY REQUESTED.
- Preserve User Content: If the input task or prompt includes extensive guidelines or examples, preserve them entirely, or as closely as possible. If they are vague, consider breaking down into sub-steps. Keep any details, guidelines, examples, variables, or placeholders provided by the user.
- Constants: DO include constants in the prompt, as they are not susceptible to prompt injection. Such as guides, rubrics, and examples.
- Output Format: Explicitly the most appropriate output format, in detail. This should include length and syntax (e.g. short sentence, paragraph, JSON, etc.)
    - For tasks outputting well-defined or structured data (classification, JSON, etc.) bias toward outputting a JSON.
    - JSON should never be wrapped in code blocks (```) unless explicitly requested.

The final prompt you output should adhere to the following structure below. Do not include any additional commentary, only output the completed system prompt. SPECIFICALLY, do not include any additional messages at the start or end of the prompt. (e.g. no "---")

[Concise instruction describing the task - this should be the first line in the prompt, no section header]

[Additional details as needed.]

[Optional sections with headings or bullet points for detailed steps.]

# Steps [optional]

[optional: a detailed breakdown of the steps necessary to accomplish the task]

# Output Format

[Specifically call out how the output should be formatted, be it response length, structure e.g. JSON, markdown, etc]

# Examples [optional]

[Optional: 1-3 well-defined examples with placeholders if necessary. Clearly mark where examples start and end, and what the input and output are. User placeholders as necessary.]
[If the examples are shorter than what a realistic example is expected to be, make a reference with () explaining how real examples should be longer / shorter / different. AND USE PLACEHOLDERS! ]

# Notes [optional]

[optional: edge cases, details, and an area to call or repeat out specific important considerations]
""".strip()


def generate_prompt(task_or_prompt: str):
    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "system",
                "content": META_PROMPT,
            },
            {
                "role": "user",
                "content": "Task, Goal, or Current Prompt:\n" + task_or_prompt,
            },
        ],
    )
    return completion.choices[0].message.content


if __name__ == '__main__':
    prompt_1 = """
    你是一位资深全栈工程师，设计工程师，拥有丰富的全栈开发经验及极高的审美造诣，擅长现代化设计风格，擅长移动端设计及开发。

你要做什么
用户将提出一个【健康类 APP】
参考 ui_ux_design 设计这个【健康类 APP】，模拟产品经理提出需求和信息架构，请自己构思好功能需求和界面
下面这两个步骤，每一个小功能（根据功能划分，可能有多个页面）就输出一个html，输出完成后提示用户是否继续，如果用户输入继续，则继续根据按照下面步骤输出下一个功能的 UI/UX 参考图

然后使用 html + tailwindcss 设计 UI/UX 参考图
调用【Artifacts】插件可视化预览该 UI/UX 图（可视化你编写的 html 代码）
要高级有质感（运用玻璃拟态等视觉效果），遵守设计规范，注重UI细节
请引入 tailwindcss CDN 来完成，而不是编写 style 样式，图片使用 unslash，界面中不要有滚动条出现
图标使用 Lucide Static CDN 方式引入，如https://unpkg.com/lucide-static@latest/icons/XXX.svg，而不是手动输出 icon svg 路径

将一个功能的所有页面写入到一个 html 中（为每个页面创建简单的 mockup 边框预览，横向排列），每个页面在各自的 mockup 边框内相互独立，互不影响
思考过程仅思考功能需求、设计整体风格等，不要在思考时就写代码，仅在最终结果中输出代码
    """


    prompt_2 = """
    根据下面资料，设计一张SVG知识图表，主题为【xxx】，目的是【解释概念/呈现数据/传达流程/启发思考】。目标受众是【大学生】,他们偏好【条理清晰、逻辑性强、易于理解】的设计。语言：中文 画布大小：宽高比例为3:4
- 信息结构：按照【问题一过程一解决方案】的逻辑，用图像化方式呈现信息。
- 视觉层次：重点使用【字体加粗、色彩对比、尺寸放大】突显，并用【箭头、连接线】引导视线。
- 配色设计：保持专业现代感，让颜色更丰富。
- 图示风格：选用【扁平化设计】图示，统一风格且直观易懂，适度加上图标。
- 构图与排版：依【标题一重点一细节】的层次结构，使用【色块、线条】划分区块，保持适当留白。
风格要求：设计风格以【专业商务风格】为主，整体设计应【简约、现代、易于理解】。
资料：「要配图的内容，可以直接复制粘贴整篇文章，也可以是一个段落」
    
    """


    main_prompt = generate_prompt(prompt_2)
    print(main_prompt)