from crawl4ai.extraction_strategy import LLMExtractionStrategy
from crawl4ai.web_crawler import WebCrawler
from crawl4ai.crawler_strategy import *


def on_driver_created(driver):
    print("[HOOK] on_driver_created")
    # Example customization: maximize the window
    driver.maximize_window()

    # Example customization: logging in to a hypothetical website
    # driver.get('https://example.com/login')
    #
    # from selenium.webdriver.support.ui import Web<PERSON>riverWait
    # from selenium.webdriver.common.by import By
    # from selenium.webdriver.support import expected_conditions as EC
    #
    # WebDriverWait(driver, 10).until(
    #     EC.presence_of_element_located((By.NAME, 'username'))
    # )
    # driver.find_element(By.NAME, 'username').send_keys('testuser')
    # driver.find_element(By.NAME, 'password').send_keys('password123')
    # driver.find_element(By.NAME, 'login').click()
    # WebDriverWait(driver, 10).until(
    #     EC.presence_of_element_located((By.ID, 'welcome'))
    # )
    # Add a custom cookie
    driver.add_cookie({'name': 'zsxq_access_token', 'value': '794A907A-7C64-F51F-DFE9-0E2193CD2E88_D40479E056C3316F'})
    return driver

def before_get_url(driver):
    print("[HOOK] before_get_url")
    # Example customization: add a custom header
    # Enable Network domain for sending headers
    driver.execute_cdp_cmd('Network.enable', {})
    # Add a custom header
    ZSXQ_ACCESS_TOKEN = '794A907A-7C64-F51F-DFE9-0E2193CD2E88_D40479E056C3316F'  # 登录后Cookie中的Token（必须修改）
    USER_AGENT = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
    headers = {
        'Cookie': 'abtest_env=product;zsxq_access_token=' + ZSXQ_ACCESS_TOKEN,
        'User-Agent': USER_AGENT,
        'accept': 'application/json, text/plain, */*',
        'sec-ch-ua-platform': '"Windows"',
        'origin': 'https://wx.zsxq.com',
        'sec-fetch-site': 'same-site',
        'sec-fetch-mode': 'cors',
        'sec-fetch-dest': 'empty',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="98", "Google Chrome";v="98"',
        'referer': 'https://wx.zsxq.com/',
        'dnt': '1',
    }

    driver.execute_cdp_cmd('Network.setExtraHTTPHeaders', {'headers': headers})
    return driver


def after_get_url(driver):
    print("[HOOK] after_get_url")
    # Example customization: log the URL
    print(driver.current_url)
    return driver


def before_return_html(driver, html):
    print("[HOOK] before_return_html")
    # Example customization: log the HTML
    print(len(html))
    return driver


if __name__ == '__main__':
    crawler_strategy = LocalSeleniumCrawlerStrategy(verbose=True)
    crawler_strategy.set_hook('on_driver_created', on_driver_created)
    crawler_strategy.set_hook('before_get_url', before_get_url)
    crawler_strategy.set_hook('after_get_url', after_get_url)
    crawler = WebCrawler(verbose=True, crawler_strategy=crawler_strategy)
    crawler.warmup()

    strategy = LLMExtractionStrategy(
        provider='gpt-4o',
        api_token='sk-WZz92Lwlz8NDmNhO788d4aA18e374c95B97aA9A339858e82',
        base_url='https://aihubmix.com/v1'
        # instruction="提取文章中的标题、作者、发布时间，并且对内容进行摘要总结"
    )

    result = crawler.run(url="https://articles.zsxq.com/id_eblx8pcdjkqw.html",
                         extraction_strategy=strategy,
                         # css_selector="h1.title",
                         screenshot=True,
                         bypass_cache=True)

    h = html2text.HTML2Text()
    # h.ignore_links = True
    # h.ignore_images = True
    h.ignore_emphasis = True
    h.body_width = 0

    markdown_content = h.handle(result.html)
    print(markdown_content)

    if result.success:
        print("Crawl succeeded!")
        print("URL:", result.url)
        print("HTML:", result.html[:100])  # Print the first 100 characters of the HTML

        # 清理后的网页 HTML 内容。此字段在删除不需要的标签（例如<script> 、 <style>以及其他不贡献有用内容的标签）后保存 HTML。
        print("Cleaned HTML:", result.cleaned_html[:200])
        """
        包含从网页中提取的媒体元素列表的字典。媒体元素分为图像、视频和音频。它们的结构如下：

        Images: Each image is represented as a dictionary with src (source URL) and alt (alternate text).
        图像：每个图像都表示为带有src （源 URL）和alt （替代文本）的字典。
        Videos: Each video is represented similarly with src and alt.
        视频：每个视频都用类似的src和alt表示。
        Audios: Each audio is represented with src and alt.
        音频：每个音频都用src和alt表示。
        """
        print("Media:", result.media)
        # 内部链接：指向同一域的链接。外部链接：指向不同域的链接。
        print("Links:", result.links)
        with open("screenshot.png", "wb") as f:
            f.write(base64.b64decode(result.screenshot))
        # Base64 编码的网页屏幕截图。如果爬网配置为截取屏幕截图，则此字段存储屏幕截图数据。
        # print("Screenshot:", result.screenshot)
        # 将网页内容转换为 Markdown 格式。这对于生成保留原始 HTML 结构的干净、可读的文本非常有用。
        print("Markdown:", result.markdown)
        # 根据指定的提取策略提取的内容。该字段保存从网页中提取的有意义的内容块，为您的人工智能和数据处理需求做好准备。
        print("Extracted Content:", result.extracted_content)
        # 包含从网页提取的元数据的字典，例如标题、描述、关键字和其他元标记。
        print("Metadata:", result.metadata)
    else:
        print("Crawl failed with error:", result.error_message)
