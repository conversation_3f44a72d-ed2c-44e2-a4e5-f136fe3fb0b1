<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>v0 Pricing</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-white font-sans">
    <header class="flex justify-between items-center p-4 border-b">
        <div class="flex items-center">
            <img src="/placeholder.svg?height=32&width=32" alt="v0 Logo" class="h-8 w-8 mr-2">
            <span class="font-bold text-lg">Pricing</span>
        </div>
        <div class="flex items-center">
            <button class="bg-black text-white px-4 py-2 rounded-full mr-2">New Generation</button>
            <button class="text-gray-600 px-4 py-2 rounded-full">Feedback</button>
            <div class="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-500 rounded-full ml-2"></div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <h1 class="text-4xl font-bold text-center mb-4">Pricing</h1>
        <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            We want to empower every builder to learn coding best practices, create beautiful interfaces, and fully functioning apps. From individuals to enterprises, we have a plan that fits your use case.
        </p>

        <div class="grid md:grid-cols-3 gap-8">
            <!-- Free Plan -->
            <div class="border rounded-lg p-6">
                <h2 class="text-2xl font-bold mb-2">Free</h2>
                <p class="text-gray-600 mb-4">For individuals that just want to explore.</p>
                <p class="text-4xl font-bold mb-6">$0<span class="text-lg font-normal text-gray-600">/month</span></p>
                <button class="w-full bg-gray-200 text-gray-800 py-2 rounded-md mb-6">Current Plan</button>
                <ul class="space-y-2">
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Access to v0.dev/chat <span class="ml-2 bg-black text-white text-xs px-2 py-1 rounded-full">New</span></li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>200 credits/month</li>
                </ul>
            </div>

            <!-- Premium Plan -->
            <div class="border rounded-lg p-6 border-blue-200">
                <h2 class="text-2xl font-bold mb-2">Premium</h2>
                <p class="text-gray-600 mb-4">For users that want more messages and generations.</p>
                <p class="text-4xl font-bold mb-6">$20<span class="text-lg font-normal text-gray-600">/month</span></p>
                <button class="w-full bg-black text-white py-2 rounded-md mb-6">Choose Premium</button>
                <ul class="space-y-2">
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Higher usage limits on v0.dev/chat <span class="ml-2 bg-black text-white text-xs px-2 py-1 rounded-full">New</span></li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>5000 credits/month</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Optional credits purchase</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Vision generations</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Custom themes</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Private generations</li>
                </ul>
            </div>

            <!-- Enterprise Plan -->
            <div class="border rounded-lg p-6">
                <h2 class="text-2xl font-bold mb-2">Enterprise</h2>
                <p class="text-gray-600 mb-4">For companies and teams that require robust features and higher limits.</p>
                <p class="text-4xl font-bold mb-6">Contact Us</p>
                <button class="w-full bg-white text-black border border-black py-2 rounded-md mb-6">Talk to Sales</button>
                <ul class="space-y-2">
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Custom usage limits on v0.dev/chat <span class="ml-2 bg-black text-white text-xs px-2 py-1 rounded-full">New</span></li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Custom credits/month</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Optional credits purchase</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Vision generations</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Custom themes</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Private generations</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>SAML SSO</li>
                    <li class="flex items-center"><svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>7 other features</li>
                </ul>
            </div>
        </div>

        <div class="mt-16 text-center">
            <h2 class="text-3xl font-bold mb-4">AI-powered custom components</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Create elegant and sophisticated components in just a few prompts. Export the code with 1-click and use in your own projects.
            </p>
        </div>
    </main>
</body>
</html>