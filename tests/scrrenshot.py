import base64
from pathlib import Path
from crawl4ai.web_crawler import WebCrawler


def capture_full_page_screenshot(url: str, output_path: str) -> bool:
    """
    Capture a full page screenshot of a given URL using Crawl4AI.

    Args:
        url (str): The URL of the webpage to capture
        output_path (str): Path where to save the screenshot

    Returns:
        bool: True if screenshot was successful, False otherwise
    """
    try:
        # Initialize crawler
        with WebCrawler() as crawler:
            # Execute the crawl with screenshot
            result = crawler.run(
                url=url,
                screenshot=True,  # Enable screenshot capture
                js_code=[
                    # Scroll to bottom to ensure all content is loaded
                    "window.scrollTo(0, document.body.scrollHeight);",
                    # Wait a bit for any lazy-loaded content
                    "return new Promise(resolve => setTimeout(resolve, 2000));",
                    # Scroll back to top
                    "window.scrollTo(0, 0);"
                ],
                # Wait for page to be fully loaded
                wait_for="js:() => document.readyState === 'complete'",
                # Add some delay before taking screenshot
                delay_before_return_html=2.0,
                # Remove any overlay elements that might block content
                remove_overlay_elements=True
            )

            # Check if screenshot was captured successfully
            if result.success and result.screenshot:
                # Decode base64 screenshot data and save to file
                screenshot_data = base64.b64decode(result.screenshot)

                # Create directory if it doesn't exist
                output_dir = Path(output_path).parent
                output_dir.mkdir(parents=True, exist_ok=True)

                # Save the screenshot
                with open(output_path, 'wb') as f:
                    f.write(screenshot_data)

                print(f"Screenshot saved successfully to: {output_path}")
                return True
            else:
                print("Failed to capture screenshot")
                if result.error_message:
                    print(f"Error: {result.error_message}")
                return False

    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return False


# Example usage
if __name__ == "__main__":
    url = "https://urltoui.vercel.app"  # Replace with your target URL
    output_path = "screenshots/full_page.png"

    capture_full_page_screenshot(url, output_path)