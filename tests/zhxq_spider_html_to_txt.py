import scrapy
from scrapy.crawler import CrawlerProcess
from bs4 import BeautifulSoup
import html2text
import os
import json
from urllib.parse import urlparse


ZSXQ_ACCESS_TOKEN = '794A907A-7C64-F51F-DFE9-0E2193CD2E88_D40479E056C3316F'  # 登录后Cookie中的Token（必须修改）
USER_AGENT = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
headers = {
    'Cookie': 'abtest_env=product;zsxq_access_token=' + ZSXQ_ACCESS_TOKEN,
    'User-Agent': USER_AGENT,
    'accept': 'application/json, text/plain, */*',
    'sec-ch-ua-platform': '"Windows"',
    'origin': 'https://wx.zsxq.com',
    'sec-fetch-site': 'same-site',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="98", "Google Chrome";v="98"',
    'referer': 'https://wx.zsxq.com/',
    'dnt': '1',
}


class ZsxqSpider(scrapy.Spider):
    name = "zsxq_spider"
    start_urls = ["https://articles.zsxq.com/id_x5br0ho3umzi.html"]

    def __init__(self, *args, **kwargs):
        super(ZsxqSpider, self).__init__(*args, **kwargs)
        self.h = html2text.HTML2Text()
        self.h.ignore_links = True
        self.h.ignore_images = True
        self.h.ignore_emphasis = True
        self.h.body_width = 0
        self.results = []

        os.makedirs('.data', exist_ok=True)
        os.makedirs('.data/markdown_files', exist_ok=True)

    def start_requests(self):
        yield scrapy.Request(url=self.start_urls[0], headers=headers, callback=self.parse)

    def parse(self, response):
        title = response.css('title::text').get()
        content = response.css('div.content').get()  # 请根据实际内容结构调整选择器
        content_md = self.convert_to_md(content)

        with open('output.md', 'w', encoding='utf-8') as f:
            f.write(f"# {title}\n\n")
            f.write(content_md)

        self.log(f'Successfully written to output.md')

    def convert_to_md(self, html_content):
        # 将 HTML 转换为 Markdown
        from markdownify import markdownify as md
        return md(html_content)

    # def parse(self, response):
    #     # 使用 BeautifulSoup 提取主要内容
    #     soup = BeautifulSoup(response.text, 'html.parser')
    #
    #     # 移除导航栏、侧边栏、页脚等元素
    #     for elem in soup(['nav', 'header', 'footer', 'aside']):
    #         elem.decompose()
    #
    #     # 尝试找到主要内容区域
    #     main_content = soup.find('main') or soup.find('article') or soup.find('div', class_='content')
    #
    #     if main_content:
    #         content = str(main_content)
    #     else:
    #         content = str(soup.body)  # 如果找不到明确的主要内容，使用整个 body
    #
    #     # 转换为 Markdown
    #     markdown_content = self.h.handle(content)
    #
    #     # 生成文件名并保存
    #     parsed_url = urlparse(response.url)
    #     file_path = parsed_url.path.strip('/').replace('/', '_') or 'index'
    #     markdown_filename = f'.data/markdown_files/{file_path}.md'
    #
    #     with open(markdown_filename, 'w', encoding='utf-8') as f:
    #         f.write(markdown_content)
    #
    #     result = {
    #         'url': response.url,
    #         'markdown_file': markdown_filename,
    #     }
    #     self.results.append(result)
    #
    #     # 继续爬取其他链接
    #     # for link in response.css('a::attr(href)').getall():
    #     #     yield response.follow(link, self.parse)
    #
    # def closed(self, reason):
    #     with open('.data/markdown_results.json', 'w', encoding='utf-8') as f:
    #         json.dump(self.results, f, ensure_ascii=False, indent=2)
    #
    #     print(f"爬取完成。总共爬取了 {len(self.results)} 个页面")
    #     print("结果元数据保存在 .data/markdown_results.json")
    #     print("Markdown 文件保存在 .data/markdown_files/ 目录下")


if __name__ == '__main__':
    process = CrawlerProcess()
    process.crawl(ZsxqSpider)
    process.start()
