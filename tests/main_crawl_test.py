import base64

from crawl4ai import WebCrawler


def create_crawler():
    crawler = WebCrawler(verbose=True)
    crawler.warmup()
    return crawler


"""
    This is a simple example of how to use the WebCrawler class.
"""
crawler = create_crawler()

if __name__ == '__main__':
    # result = crawler.run(url="https://www.nbcnews.com/business")
    # print(f"Basic crawl result: {result}")

    # 转换成图片
    # result = crawler.run(url="https://www.nbcnews.com/business", screenshot=True)
    # with open("screenshot.png", "wb") as f:
    #     f.write(base64.b64decode(result.screenshot))
    # print("Screenshot saved to 'screenshot.png'!")

    # CSS选择器
    # result = crawler.run(
    #     url="https://www.nbcnews.com/business",
    #     css_selector="h2"
    # )
    # print(f"CSS Selector (H2 tags) result: {result}")

    print("end...")
