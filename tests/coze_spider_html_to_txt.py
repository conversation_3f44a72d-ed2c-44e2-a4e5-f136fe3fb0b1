import os
import json
import requests
from bs4 import BeautifulSoup
import html2text
from urllib.parse import urljoin, urlparse
from pathlib import Path
import time


class CozeDocsExtractor:
    def __init__(self, base_url="https://www.coze.cn"):
        self.base_url = base_url
        self.h = html2text.HTML2Text()
        self.h.ignore_links = False  # 保留链接
        self.h.ignore_images = False  # 保留图片
        self.h.ignore_emphasis = False
        self.h.body_width = 0
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # 创建必要的目录
        self.data_dir = Path('.data')
        self.coze_dir = self.data_dir / 'coze'
        self.markdown_dir = self.coze_dir / 'markdown_files'

        # 创建目录结构
        self.data_dir.mkdir(exist_ok=True)
        self.coze_dir.mkdir(exist_ok=True)
        self.markdown_dir.mkdir(exist_ok=True)

    def extract_links(self, url):
        """提取页面中的所有文档链接"""
        print(f"Extracting links from {url}")
        response = self.session.get(url)
        soup = BeautifulSoup(response.text, 'html.parser')

        links = []
        for a in soup.find_all('a', href=True):
            href = a['href']
            if href.startswith('/open/docs/'):
                full_url = urljoin(self.base_url, href)
                title = a.get_text(strip=True)
                links.append({
                    'url': full_url,
                    'title': title
                })
        links.append({
            'url': 'https://www.coze.cn/open/docs/guides/workflow',
            'title': '工作流'
        })
        return links

    def extract_text_from_element(self, element):
        """从元素中提取格式化的文本内容"""
        if element.name in ['script', 'style']:
            return ''

        # 处理特殊标签
        if element.name == 'div' and 'ace-line' in element.get('class', []):
            # 获取此行的缩进级别
            indent_level = 0
            if 'list-div' in element.get('class', []):
                indent_level = 1

            text_parts = []
            for child in element.children:
                if hasattr(child, 'get_text'):
                    text = child.get_text(strip=True)
                    if text:
                        text_parts.append(text)

            text = ' '.join(text_parts)
            if text:
                return '  ' * indent_level + text + '\n'
            return '\n'

        return ''

    def debug_html_structure(self, html):
        """调试HTML结构"""
        soup = BeautifulSoup(html, 'html.parser')
        print("\nDebug HTML Structure:")
        # 打印所有class包含zone-container的div
        containers = soup.find_all('div', class_=lambda x: x and 'zone-container' in x)
        print(f"Found {len(containers)} zone containers")
        for i, container in enumerate(containers):
            print(f"\nContainer {i + 1} classes:", container.get('class'))
            print(f"First few lines of content: {container.get_text()[:200]}...")

    def clean_html_content(self, html):
        """清理HTML内容，提取主要文档内容"""
        soup = BeautifulSoup(html, 'html.parser')

        # 调试HTML结构
        self.debug_html_structure(html)

        # 尝试多种选择器找到主要内容容器
        content_container = None

        # 1. 尝试找到指定class的容器
        containers = soup.find_all('div', class_=lambda x: x and 'zone-container' in x)
        if containers:
            # 选择包含最多文本内容的容器
            content_container = max(containers, key=lambda x: len(x.get_text()))

        if not content_container:
            print("Warning: Could not find content container")
            return ''

        # 提取文本内容
        content = []

        # 处理每一行
        for line in content_container.find_all('div', recursive=True):
            classes = line.get('class', [])

            # 标题处理
            if 'heading-h2' in classes:
                text = line.get_text(strip=True)
                if text:
                    content.append(f"\n## {text}\n")
            elif 'heading-h3' in classes:
                text = line.get_text(strip=True)
                if text:
                    content.append(f"\n### {text}\n")
            elif 'ace-line' in classes:
                # 处理普通文本和列表
                text = line.get_text(strip=True)
                if text:
                    if 'list-div' in classes:
                        content.append(f"- {text}\n")
                    else:
                        content.append(f"{text}\n")

        result = '\n'.join(content)
        print(f"\nExtracted content length: {len(result)} characters")
        print("First 200 characters of content:", result[:200])
        return result

    def save_markdown(self, content, file_path):
        """保存Markdown内容到文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def generate_filename(self, url, title):
        """根据URL和标题生成文件名"""
        # 从URL中提取路径部分
        path = urlparse(url).path
        # 移除开头的/open/docs/
        path = path.replace('/open/docs/', '')
        # 替换/为_
        path = path.replace('/', '_')
        # 如果路径为空，使用标题
        if not path:
            path = title
        # 清理文件名
        path = "".join(x for x in path if x.isalnum() or x in ('_', '-'))
        return f"{path}.md"

    def process_single_doc(self, url, title):
        """处理单个文档页面"""
        print(f"\nProcessing document: {title} ({url})")

        try:
            # 获取页面内容
            response = self.session.get(url)
            response.raise_for_status()  # 检查请求是否成功

            print(f"Response status code: {response.status_code}")
            print(f"Response content length: {len(response.text)} characters")

            # 添加延迟，避免请求过快
            time.sleep(1)

            # 提取并清理内容
            content = self.clean_html_content(response.text)
            if not content:
                print(f"Warning: No content extracted from {url}")
                return None

            # 生成文件名并保存
            filename = self.generate_filename(url, title)
            file_path = self.markdown_dir / filename

            # 添加标题
            full_content = f"# {title}\n\n{content}"

            self.save_markdown(full_content, file_path)

            return {
                'url': url,
                'title': title,
                'markdown_file': str(file_path)
            }

        except Exception as e:
            print(f"Error processing {url}: {str(e)}")
            return None

    def run(self, start_url):
        """运行文档提取器的主函数"""
        print("Starting Coze documentation extraction...")

        # 1. 提取所有文档链接
        all_links = self.extract_links(start_url)
        print(f"Found {len(all_links)} documents")

        # 2. 保存链接列表
        links_file = self.coze_dir / 'doc_links.json'
        with open(links_file, 'w', encoding='utf-8') as f:
            json.dump(all_links, f, ensure_ascii=False, indent=2)

        # 3. 处理每个文档
        results = []
        for link in all_links:
            result = self.process_single_doc(link['url'], link['title'])
            if result:
                results.append(result)

        # 4. 保存处理结果
        results_file = self.coze_dir / 'processing_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\nExtraction completed:")
        print(f"- Total documents found: {len(all_links)}")
        print(f"- Successfully processed: {len(results)}")
        print(f"- Links saved to: {links_file}")
        print(f"- Results saved to: {results_file}")
        print(f"- Markdown files saved in: {self.markdown_dir}")


if __name__ == '__main__':
    # 使用示例
    extractor = CozeDocsExtractor()
    extractor.run('https://www.coze.cn/open/docs/guides/workflow')